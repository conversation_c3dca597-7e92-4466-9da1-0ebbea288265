import numpy as np
import torch
import torch.nn.functional as F
from torch import nn


def fuse_bn(conv, bn, scale=1):
    kernel = conv.weight if not isinstance(conv, torch.Tensor) else conv
    running_mean = bn.running_mean * scale
    running_var = bn.running_var
    gamma = bn.weight
    beta = bn.bias * scale
    eps = bn.eps
    std = (running_var + eps).sqrt()
    t = (gamma / std).reshape(-1, 1, 1, 1)
    return kernel * t, beta - running_mean * gamma / std


def fuse_fc(fc_layers, scale=1):
    w, b = 0, 0
    for fc in fc_layers:
        w += fc.weight.data
        b += fc.bias.data * scale
    return w, b


def expend_kernel(kernel, target_kernel_size):
    if isinstance(target_kernel_size, int):
        target_kernel_size = (target_kernel_size, target_kernel_size)
    H_pixels_to_pad = (target_kernel_size[0] - kernel.size(2)) // 2
    W_pixels_to_pad = (target_kernel_size[1] - kernel.size(3)) // 2
    return F.pad(kernel, [W_pixels_to_pad, W_pixels_to_pad, H_pixels_to_pad, H_pixels_to_pad])


def merge_1x1_kxk(k1, b1, k2, b2, groups=1):
    if groups == 1:
        k = F.conv2d(k2, k1.permute(1, 0, 2, 3))
        b_hat = (k2 * b1.reshape(1, -1, 1, 1)).sum((1, 2, 3))
    else:
        k_slices = []
        b_slices = []
        k1_T = k1.permute(1, 0, 2, 3)
        k1_group_width = k1.size(0) // groups
        k2_group_width = k2.size(0) // groups
        for g in range(groups):
            k1_T_slice = k1_T[:, g * k1_group_width:(g + 1) * k1_group_width, :, :]
            k2_slice = k2[g * k2_group_width:(g + 1) * k2_group_width, :, :, :]
            k_slices.append(F.conv2d(k2_slice, k1_T_slice))
            b_slices.append(
                (k2_slice * b1[g * k1_group_width:(g + 1) * k1_group_width].reshape(1, -1, 1, 1)).sum((1, 2, 3)))
        k, b_hat = torch.cat(k_slices, dim=0), torch.cat(b_slices)
    return k, b_hat + b2


def avg_to_kernel(channels, kernel_size, groups):
    kernel_size = kernel_size[0]
    input_dim = channels // groups
    k = torch.zeros((channels, input_dim, kernel_size, kernel_size))
    k[np.arange(channels), np.tile(np.arange(input_dim), groups), :, :] = 1.0 / kernel_size ** 2
    return k


def get_equivalent_kernel_bias(convbn, scale):
    eq_k, eq_b = 0, 0
    for i in range(len(convbn)):
        k, b = fuse_bn(convbn[i][0], convbn[i][1], scale)
        eq_k += k
        eq_b += b
    return eq_k, eq_b


class BNAndPadLayer(nn.Module):
    def __init__(self,
                 pad_pixels,
                 num_features,
                 eps=1e-5,
                 momentum=0.1,
                 affine=True,
                 track_running_stats=True):
        super(BNAndPadLayer, self).__init__()
        self.bn = nn.BatchNorm2d(num_features, eps, momentum, affine, track_running_stats)
        self.pad_pixels = pad_pixels

    def forward(self, input):
        output = self.bn(input)
        if self.pad_pixels > 0:
            if self.bn.affine:
                pad_values = self.bn.bias.detach() - self.bn.running_mean * self.bn.weight.detach() / torch.sqrt(
                    self.bn.running_var + self.bn.eps)
            else:
                pad_values = - self.bn.running_mean / torch.sqrt(self.bn.running_var + self.bn.eps)
            output = F.pad(output, [self.pad_pixels] * 4)
            pad_values = pad_values.view(1, -1, 1, 1)
            output[:, :, 0:self.pad_pixels, :] = pad_values
            output[:, :, -self.pad_pixels:, :] = pad_values
            output[:, :, :, 0:self.pad_pixels] = pad_values
            output[:, :, :, -self.pad_pixels:] = pad_values
        return output

    @property
    def weight(self):
        return self.bn.weight

    @property
    def bias(self):
        return self.bn.bias

    @property
    def running_mean(self):
        return self.bn.running_mean

    @property
    def running_var(self):
        return self.bn.running_var

    @property
    def eps(self):
        return self.bn.eps
